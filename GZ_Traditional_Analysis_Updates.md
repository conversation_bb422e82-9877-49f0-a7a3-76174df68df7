# GZ Traditional Analysis 更新说明

## 修改概述

根据用户要求，对 `Pile_analyze_GZ_gui_INT_S2.py` 中的 GZ Traditional Analysis 分析计算与报告进行了以下修改：

## 1. K值计算深度范围默认设置修改

**修改位置**: 第1611行
**修改内容**: 将K值计算深度范围的默认值从 `False`（未勾选）改为 `True`（默认勾选）

```python
# 修改前
'gz_enable_depth_range': tk.BooleanVar(value=False),  # 默认不启用深度范围

# 修改后  
'gz_enable_depth_range': tk.BooleanVar(value=True),  # 默认启用深度范围
```

## 2. 新增50cm连续K值检查函数

**新增函数**: `check_50cm_continuous_K`
**位置**: 第370-426行
**功能**: 检查是否存在某深度50cm范围内K(i)值均为指定值的情况

该函数实现了用户要求的连续性检查：
- 检查K(i)=2，K(i+10cm)=2，K(i+20cm)=2，K(i+30cm)=2，K(i+40cm)=2，K(i+50cm)=2
- 支持K=2和K=3的连续性检查
- 使用10%容差匹配，处理浮点数精度问题

## 3. 重写判定依据函数

**修改函数**: `determine_final_category`
**位置**: 第723-840行
**修改内容**: 完全重写判定逻辑，实现新的分类规则

### 新的判定依据

#### I类桩
- 所有检测截面Ki值均为1

#### II类桩（并列条件，满足一个即判定为II类桩）
1. 所有检测截面仅存在一个K(i)=2的情况，且不存在Ki=3、Ki=4
2. 存在多个Ki=2，但不存在Ki=3、Ki=4，且不存在某深度50cm范围内K(i)值均为2

#### III类桩（并列条件，满足一个即判定为III类桩）
1. 所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4
2. 存在多个Ki=3，且不存在Ki=4，且任意两个相邻的Ki=3截面距离≥50cm
3. 存在多个Ki=2，且不存在Ki=3、Ki=4，且存在某深度50cm范围内K(i)值均为2

#### IV类桩（并列条件，满足一个即判定为IV类桩）
1. 所有检测截面仅存在一个K(i)=4的情况
2. 存在多个Ki=3，且不存在Ki=4，且存在某深度50cm范围内K(i)值均为3

## 4. 更新报告生成

**修改函数**: `generate_gz_analysis_summary`
**位置**: 第2755-2800行
**修改内容**: 
- 更新默认深度范围启用状态显示
- 添加新版分类规则说明
- 增加判定依据的详细说明

## 5. 简化函数调用

**修改位置**: 第2527行
**修改内容**: 简化`determine_final_category`函数调用，移除不再使用的`gz_depth_range`参数

```python
# 修改前
if gz_enable_depth_range:
    final_category, report_details = determine_final_category(results['K_values'], gz_depth_range)
else:
    final_category, report_details = determine_final_category(results['K_values'], None)

# 修改后
final_category, report_details = determine_final_category(results['K_values'])
```

## 技术特点

1. **精确的50cm连续性检查**: 使用6个测点（0cm, 10cm, 20cm, 30cm, 40cm, 50cm）进行连续性验证
2. **容差匹配**: 10%的深度间隔容差，处理实际测量中的微小偏差
3. **详细的调试信息**: 添加了详细的调试输出，便于问题排查
4. **向后兼容**: 保持了原有的配置接口，只是改变了默认值和判定逻辑

## 验证

- 脚本编译成功，无语法错误
- 保持了原有的GUI界面和配置选项
- 新的判定依据已集成到分析流程和报告生成中

## 注意事项

1. 新的判定依据更加严格和精确
2. 50cm连续性检查基于10cm间隔的测点
3. 默认启用深度范围计算，用户仍可手动关闭
4. 所有修改都保持了与现有代码的兼容性
